{"name": "tempest/app", "require": {"tempest/framework": "^1.2"}, "require-dev": {"phpunit/phpunit": "^10.2 || ^11.5.15", "symfony/var-dumper": "^7.2.3", "carthage-software/mago": "^0.20.4"}, "autoload": {"psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Tests\\Tempest\\": "vendor/brendt/tempest/tests/"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "scripts": {"post-create-project-cmd": ["@php ./vendor/bin/tempest install framework -f", "@php ./tempest discovery:generate --no-interaction"], "post-autoload-dump": ["@php ./vendor/bin/tempest discovery:generate --no-interaction"], "phpunit": "vendor/bin/phpunit --display-warnings --display-skipped --display-deprecations --display-errors --display-notices", "mago:fmt": "vendor/bin/mago fmt", "mago:lint": "vendor/bin/mago lint --fix && vendor/bin/mago lint", "qa": ["composer mago:fmt", "composer <PERSON><PERSON><PERSON><PERSON>", "composer mago:lint"]}, "license": "MIT", "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"php-http/discovery": true, "carthage-software/mago": true}}}