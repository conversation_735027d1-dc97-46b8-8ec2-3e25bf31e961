<?php

declare(strict_types=1);

namespace Tests;

use PHPUnit\Framework\Attributes\DataProvider;
use Tempest\Http\Status;

class HttpControllerTest extends IntegrationTestCase
{
    #[DataProvider('routes')]
    public function test_something(string $route)
    {
        $response = $this->http->get($route)
            ->assertStatus(Status::OK)
            ->assertJsonContains([
                'title' => 'Some Other Title',
            ]);

//        lw($response->body);
    }

    public static function routes(): \Generator
    {
        yield 'working' => ['/working'];
        yield 'not-working' => ['/not-working'];
    }
}
