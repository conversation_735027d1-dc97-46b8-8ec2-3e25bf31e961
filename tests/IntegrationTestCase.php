<?php

declare(strict_types=1);

namespace Tests;

use App\Seeders\Books;
use Override;
use Tempest\Database\Migrations\MigrationManager;
use Tempest\Discovery\DiscoveryLocation;
use Tempest\Framework\Testing\IntegrationTest;

abstract class IntegrationTestCase extends IntegrationTest
{
    protected string $root = __DIR__ . '/../';

    #[Override]
    protected function setUp(): void
    {
//        $this->discoveryLocations = [
//            new DiscoveryLocation(namespace: 'Tests\\Fixtures', path: $this->root . '/Fixtures'),
//        ];

        parent::setUp();

        $this->container->get(MigrationManager::class)->dropAll();
        $this->container->get(MigrationManager::class)->up();
        new Books()->run(null);
    }
}
