<?php

namespace App\Migrations;

use Tempest\Database\DatabaseMigration;
use Tempest\Database\QueryStatement;
use Tempest\Database\QueryStatements\CreateTableStatement;
use Tempest\Database\QueryStatements\DropTableStatement;

final class Books implements DatabaseMigration
{
    public string $name = '2025-08-23_books';

    public function up(): QueryStatement
    {
        return new CreateTableStatement('books')
            ->primary()
            ->belongsTo('books.author_id', 'authors.id', nullable: true)
            ->string('title');
    }

    public function down(): QueryStatement
    {
        return new DropTableStatement('books');
    }
}
