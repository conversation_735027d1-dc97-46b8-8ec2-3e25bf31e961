<?php

namespace App\Migrations;

use Tempest\Database\DatabaseMigration;
use Tempest\Database\QueryStatement;
use Tempest\Database\QueryStatements\CreateTableStatement;
use Tempest\Database\QueryStatements\DropTableStatement;

final class Authors implements DatabaseMigration
{
    public string $name = '2025-08-23_authors';

    public function up(): QueryStatement
    {
        return new CreateTableStatement('authors')
            ->primary()
            ->string('name');
    }

    public function down(): QueryStatement
    {
        return new DropTableStatement('authors');
    }
}
