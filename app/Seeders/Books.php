<?php

declare(strict_types=1);

namespace App\Seeders;

use App\Models\Author;
use App\Models\Book;
use Tempest\Database\DatabaseSeeder;
use UnitEnum;

use function Tempest\Database\query;

final class Books implements DatabaseSeeder
{
  public function run(null|string|UnitEnum $database): void
  {
    $author_id = query(Author::class)
      ->insert(
        name: 'Author Name',
      )
      ->execute();

    query(Book::class)
      ->insert(
        title: 'Some Title',
        author_id: $author_id,
      )
      ->onDatabase($database)
      ->execute();

    query(Book::class)
      ->insert(
        title: 'Some Other Title',
      )
      ->onDatabase($database)
      ->execute();
  }
}
