<?php

declare(strict_types=1);

namespace App;

use App\Models\Book;
use Tempest\Http\Response;
use Tempest\Http\Responses\Json;
use Tempest\Http\Responses\Ok;
use Tempest\Router\Get;

use function Tempest\Database\query;

final readonly class Controller
{
    #[Get('/working')]
    public function workingRoute(): Response
    {
        // Working.
        //
        // Query response:
        // array:1 [▼
        //     0 => array:4 [▼
        //         "books.title" => "Some Name"
        //         "books.id" => 1
        //         "author.name" => "Author Name"
        //         "author.id" => 1
        //     ]
        // ]

        $books = query(Book::class)
            ->select()
            ->with('author')
            ->where('books.id = ?', 1)
            ->all();

        return new Json($books);
    }

    #[Get('/not-working')]
    public function notWorking()
    {
        // Not working
        //
        // Query response:
        // array:1 [▼
        //     0 => array:4 [▼
        //         "books.title" => "Some Name"
        //         "books.id" => 2
        //         "author.name" => null
        //         "author.id" => null
        //     ]
        // ]

        $books = query(Book::class)
            ->select()
            ->with('author')
            ->where('books.id = ?', 2)
            ->all();

        return new Json($books);
    }
}
